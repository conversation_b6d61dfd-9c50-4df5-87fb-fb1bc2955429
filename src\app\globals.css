/* Import Design System Foundation */
@import '../styles/design-system.css';

/* stylelint-disable unknownAtRules */
@tailwind base;
@tailwind components;
@tailwind utilities;
/* stylelint-enable unknownAtRules */

:root {
  --background: #ffffff;
  --foreground: #171717;
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  /* Theme colors */
  --theme-color: #3880ff;
  --theme-color-light: color-mix(in srgb, var(--theme-color), white 20%);
  --theme-color-dark: color-mix(in srgb, var(--theme-color), black 20%);

  /* Text colors */
  --text-primary: rgb(17, 24, 39);
  --text-secondary: rgb(55, 65, 81);
  --text-high-contrast: rgb(0, 0, 0);

  /* Extended palette */
  --blue-theme: #3880ff;
  --purple-theme: #5260ff;
  --green-theme: #2dd36f;
  --orange-theme: #ff9500;
  --teal-theme: #00b4d8;
  --color-cream: #f5f1e4;
  --color-dark-sepia: #483c32;
  --color-blueprint-blue: #1c71a6;
  --color-muted-red: #d9534f;
  --color-cream-alpha-90: rgba(245, 241, 228, 0.9);
  --color-cream-alpha-20: rgba(245, 241, 228, 0.2);
  --color-cream-alpha-10: rgba(245, 241, 228, 0.1);
  --color-dark-sepia-alpha-90: rgba(72, 60, 50, 0.9);
  --color-dark-sepia-alpha-20: rgba(72, 60, 50, 0.2);
  --color-dark-sepia-alpha-10: rgba(72, 60, 50, 0.1);

  /* Typography scale */
  --font-sans: Arial, Helvetica, sans-serif;
  --font-mono: 'Fira Mono', 'Consolas', 'Monaco', monospace;
  --font-serif: 'Playfair Display', Georgia, serif;
  --text-xs: clamp(0.75rem, 0.72rem + 0.2vw, 0.85rem);
  --text-sm: clamp(0.85rem, 0.82rem + 0.25vw, 0.95rem);
  --text-md: clamp(1rem, 0.95rem + 0.3vw, 1.125rem);
  --text-lg: clamp(1.25rem, 1.12rem + 0.5vw, 1.5rem);
  --text-xl: clamp(1.5rem, 1.3rem + 0.7vw, 1.875rem);
  --text-2xl: clamp(1.85rem, 1.55rem + 0.9vw, 2.25rem);
  --text-display: clamp(2.4rem, 2rem + 1.4vw, 3.2rem);

  /* Spacing scale */
  --space-2xs: clamp(0.35rem, 0.3rem + 0.2vw, 0.5rem);
  --space-xs: clamp(0.5rem, 0.45rem + 0.3vw, 0.75rem);
  --space-sm: clamp(0.75rem, 0.65rem + 0.4vw, 1rem);
  --space-md: clamp(1rem, 0.9rem + 0.6vw, 1.75rem);
  --space-lg: clamp(1.5rem, 1.25rem + 0.9vw, 2.5rem);
  --space-xl: clamp(2rem, 1.6rem + 1.1vw, 3rem);

  /* Radii & elevation */
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --shadow-card: 0 14px 35px rgba(15, 23, 42, 0.12);
  --shadow-card-hover: 0 20px 45px rgba(15, 23, 42, 0.18);

  /* Surface tokens */
  --surface-card: rgba(255, 255, 255, 0.94);
  --surface-card-dark: rgba(31, 41, 55, 0.94);
  --border-card: rgba(99, 102, 241, 0.2);
  --border-card-dark: rgba(129, 140, 248, 0.35);

  /* Tile sizing */
  --tile-min-width: clamp(160px, 44vw, 220px);
  --tile-min-height: clamp(180px, 48vw, 260px);
  --tile-padding: clamp(1.25rem, 1rem + 1vw, 2.25rem);
  --tile-gap: clamp(0.75rem, 0.6rem + 0.4vw, 1.25rem);
  --tile-icon: clamp(2.25rem, 6vw, 3rem);
  --tile-icon-lg: clamp(3rem, 7vw, 4rem);
  --tile-icon-sm: clamp(1.75rem, 4.5vw, 2.25rem);

  /* Layout helpers */
  --container-max: min(100%, 1200px);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Dark mode CSS variables */
.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
}

@layer base {

  html,
  body {
    height: 100%;
  }

  html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  body {
    @apply bg-white text-gray-900;
    @apply dark:bg-gray-900 dark:text-gray-100;
    @apply font-sans transition-colors duration-300 ease-in-out;
    min-height: 100vh;
    min-height: 100dvh;
    font-size: var(--text-md);
    line-height: 1.6;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-serif;
  }
}

/* Add a subtle card background for contrast */
.card,
.bg-white {
  background: var(--surface-card);
  border: 1px solid var(--border-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-card);
  backdrop-filter: blur(10px);
}

.dark .card,
.dark .bg-white {
  background: var(--surface-card-dark);
  border-color: var(--border-card-dark);
  box-shadow: 0 18px 40px rgba(15, 23, 42, 0.32);
}
/* Card with tile layout support */
.card.tile {
  @apply flex flex-col items-center justify-center text-center;
  padding: var(--tile-padding);
  min-height: var(--tile-min-height);
  gap: var(--tile-gap);
}

.card.tile .tile-icon {
  margin-bottom: var(--space-sm);
  font-size: var(--tile-icon);
  color: var(--theme-color, #3880ff);
}

/* Dashboard section header color */
.dashboard-section-title {
  color: #5260ff;
  font-size: 1.5rem;
  font-weight: bold;
}

/* Theme utility classes */
.theme-color {
  color: var(--theme-color);
}

.theme-bg {
  background-color: var(--theme-color);
}

.theme-border {
  border-color: var(--theme-color);
}

.theme-text-light {
  color: var(--theme-color-light);
}

.theme-bg-light {
  background-color: var(--theme-color-light);
}

.theme-text-dark {
  color: var(--theme-color-dark);
}

.theme-bg-dark {
  background-color: var(--theme-color-dark);
}

/*
  card-theme: Soft theme background for cards, always readable text
  - Uses theme color as a very light background
  - Ensures text is dark in light mode, white in dark mode
*/
.card-theme {
  background: color-mix(in srgb, var(--theme-color), white 96%);
  color: #222;
}

.dark .card-theme {
  background: color-mix(in srgb, var(--theme-color), #18181b 80%);
  color: #fff;
}

.force-visible {
  background: #2563eb !important;
  color: #fff !important;
  border: 2px solid #fff !important;
  z-index: 9999 !important;
  box-shadow: 0 0 0 3px #0002 !important;
  border-radius: 0.5rem !important;
  font-weight: bold !important;
}

/* Modern Card Navigation Styles */
.nav-card {
  @apply bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700;
  @apply transition-all duration-300 ease-out;
  @apply hover:shadow-xl hover:scale-105 hover:-translate-y-1;
  @apply focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2;
  @apply flex flex-col items-center justify-center text-center;
  padding: var(--tile-padding);
  min-height: var(--tile-min-height);
  gap: var(--tile-gap);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
}
.dark .nav-card {
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.95) 0%, rgba(17, 24, 39, 0.95) 100%);
}

.nav-card:hover {
  @apply shadow-2xl;
  transform: translateY(-4px) scale(1.02);
}

.nav-card-active {
  @apply ring-2 ring-primary ring-offset-2;
  background: linear-gradient(135deg, rgba(56, 128, 255, 0.1) 0%, rgba(82, 96, 255, 0.05) 100%);
  border-color: rgba(56, 128, 255, 0.3);
}

.dark .nav-card-active {
  background: linear-gradient(135deg, rgba(56, 128, 255, 0.15) 0%, rgba(82, 96, 255, 0.1) 100%);
  border-color: rgba(56, 128, 255, 0.4);
}

.nav-card-icon {
  @apply text-gray-600 dark:text-gray-300 transition-all duration-300;
  @apply flex items-center justify-center;
  width: clamp(2.5rem, 10vw, 4rem);
  height: clamp(2.5rem, 10vw, 4rem);
  margin-bottom: var(--space-sm);
  font-size: var(--tile-icon);
  line-height: 1;
}
.nav-card-active .nav-card-icon {
  @apply text-primary scale-110;
}

.nav-card-title {
  @apply font-semibold text-gray-700 dark:text-gray-200 transition-colors duration-300;
  margin-top: var(--space-xs);
  font-size: var(--text-lg);
  line-height: 1.3;
}

.nav-card-active .nav-card-title {
  @apply text-primary;
}

.nav-card-description {
  @apply text-gray-500 dark:text-gray-400 transition-colors duration-300;
  margin-top: var(--space-xs);
  font-size: var(--text-sm);
  line-height: 1.4;
}

/* Responsive icon and text sizing */
.nav-card-title {
    @apply text-xl;
  }

  .nav-card-description {
    @apply text-base mt-3;
  }
}

/* Mobile Card Navigation */
@media (max-width: 768px) {
  .mobile-nav-grid {
    @apply grid grid-cols-2 gap-4 p-4;
  }

  .nav-card {
    @apply min-h-[120px] p-6;
  }
}

/* Desktop Card Navigation */
@media (min-width: 769px) {
  .desktop-nav-grid {
    @apply grid grid-cols-2 lg:grid-cols-4 gap-6 p-6;
  }

  .nav-card {
    @apply min-h-[140px] p-8;
  }
}

/* Animation keyframes */
@keyframes cardPulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

.nav-card-pulse {
  animation: cardPulse 2s infinite;
}

/* Tile Font Awesome Icon Styles */
.tile-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-sm);
  font-size: var(--tile-icon);
  color: var(--theme-color, #3880ff);
  transition: all 0.3s ease;
}

.tile-icon.large {
  font-size: var(--tile-icon-lg);
  margin-bottom: var(--space-md);
}

.tile-icon.small {
  font-size: var(--tile-icon-sm);
  margin-bottom: var(--space-xs);
}

/* General tile layout class for any div */
.tile {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--tile-padding);
  min-height: var(--tile-min-height);
  gap: var(--tile-gap);
  transition: all 0.3s ease;
}

/* Tile text styling for use with icons */
.tile-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: var(--space-xs);
}

.tile-title {
  font-weight: 600;
  font-size: var(--text-lg);
  line-height: 1.3;
  margin: 0;
}

.tile-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary, #6b7280);
  line-height: 1.4;
  margin: 0;
}

/* Dashboard card icon styles */
.dashboard-card-mobile .tile-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--theme-color, #3880ff);
}

.dashboard-card-mobile .tile-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
}

.dashboard-card-mobile .tile-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary, #6b7280);
}

/* Dark mode support for tiles */
.dark .tile-icon {
  color: var(--theme-color-light, #60a5fa);
}

.dark .tile-title {
  color: var(--text-primary, #f9fafb);
}

.dark .tile-subtitle {
  color: var(--text-secondary, #9ca3af);
}

.dark .dashboard-card-mobile {
  background: rgba(31, 41, 55, 0.95);
  border-color: rgba(75, 85, 99, 0.3);
}

.dark .card.tile {
  background: rgba(31, 41, 55, 0.95);
  border-color: rgba(75, 85, 99, 0.3);
}

/* Hover effects for interactive tiles */
.tile:hover .tile-icon {
  transform: scale(1.1);
  color: var(--theme-color-dark, #2563eb);
}

.dark .tile:hover .tile-icon {
  color: var(--theme-color, #3880ff);
}

/* Focus styles for accessibility */
.tile:focus-within {
  outline: 2px solid var(--theme-color, #3880ff);
  outline-offset: 2px;
}

/*
  Font Awesome Tile Usage Examples:
  
  Basic tile with icon and text:
  <div class="nav-card tile">
    <i class="fas fa-home tile-icon"></i>
    <div class="tile-text">
      <h3 class="tile-title">Home</h3>
      <p class="tile-subtitle">Dashboard</p>
    </div>
  </div>
  
  Dashboard card with large icon:
  <div class="dashboard-card-mobile">
    <i class="fas fa-chart-line tile-icon large"></i>
    <div class="tile-text">
      <h3 class="tile-title">Analytics</h3>
      <p class="tile-subtitle">View your data</p>
    </div>
  </div>
  
  Simple card tile:
  <div class="card tile">
    <i class="fas fa-user tile-icon"></i>
    <div class="tile-text">
      <h3 class="tile-title">Profile</h3>
    </div>
  </div>
  
  Available Font Awesome icon size modifiers:
  - .tile-icon (default)
  - .tile-icon.large
  - .tile-icon.small
*/

/* Dashboard Navigation Styles */
.nav-card-container {
  @apply flex flex-col;
}

.nav-card-container-active {
  @apply col-span-full;
}

/* Dashboard Navigation Grid Layouts - Maximized for full screen */
.dashboard-nav-grid-mobile {
  @apply grid grid-cols-1 gap-2 p-2 min-h-screen;
}

.dashboard-nav-grid-tablet {
  @apply grid grid-cols-2 gap-3 p-3 min-h-screen;
}

.dashboard-nav-grid-desktop {
  @apply grid grid-cols-2 lg:grid-cols-4 gap-4 p-4 min-h-screen;
}

/* Full-screen card sizing */
.dashboard-nav-card-mobile {
  @apply min-h-[calc(100vh/5-0.5rem)] p-6;
}

.dashboard-nav-card-tablet {
  @apply min-h-[calc(100vh/3-1rem)] p-8;
}

.dashboard-nav-card-desktop {
  @apply min-h-[calc(100vh/2-2rem)] p-10;
}

/* Expanded card content maximization */
.dashboard-expanded-content {
  @apply max-h-[calc(100vh-8rem)] overflow-y-auto p-4;
}

/* Responsive adjustments for expanded cards */
@media (max-width: 768px) {
  .nav-card-container-active {
    @apply col-span-1;
  }

  .dashboard-expanded-content {
    @apply max-h-[calc(100vh-6rem)] p-3;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .nav-card-container-active {
    @apply col-span-2;
  }

  .dashboard-expanded-content {
    @apply max-h-[calc(100vh-7rem)] p-4;
  }
}

@media (min-width: 1025px) {
  .nav-card-container-active {
    @apply col-span-4;
  }

  .dashboard-expanded-content {
    @apply max-h-[calc(100vh-8rem)] p-6;
  }
}

/* Calendar Compact Mode Optimizations */
.dashboard-expanded-content .calendar-compact {
  @apply h-full overflow-hidden;
}

.dashboard-expanded-content .calendar-compact .rbc-calendar {
  height: 100% !important;
  border: none !important;
  border-radius: 0 !important;
}

.dashboard-expanded-content .calendar-compact .rbc-month-view {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Modal Dashboard Styles */
.dashboard-modal-content {
  @apply p-0;
}

/* Dashboard Layout Refinements */
.dashboard-cards-grid {
  display: grid;
  width: 100%;
  gap: var(--space-md);
  padding: var(--space-md);
  grid-template-columns: repeat(auto-fit, minmax(var(--tile-min-width), 1fr));
  grid-auto-flow: dense;
  align-items: stretch;
}

.dashboard-cards-grid > * {
  min-height: var(--tile-min-height);
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: var(--tile-gap);
  border-radius: var(--radius-xl);
}

.dashboard-card-mobile,
.dashboard-card-desktop {
  width: 100%;
  min-height: var(--tile-min-height);
  padding: var(--tile-padding);
  border-radius: var(--radius-xl);
  background: var(--surface-card);
  border: 1px solid var(--border-card);
  box-shadow: var(--shadow-card);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  gap: var(--tile-gap);
  overflow: hidden;
}

.dark .dashboard-card-mobile,
.dark .dashboard-card-desktop {
  background: var(--surface-card-dark);
  border-color: var(--border-card-dark);
  box-shadow: 0 18px 40px rgba(15, 23, 42, 0.32);
}

.dashboard-card-full-width {
  grid-column: 1 / -1;
}

.dashboard-card-mobile .text-xl,
.dashboard-card-desktop .text-xl {
  font-size: clamp(1.35rem, 3.8vw, 1.85rem);
  line-height: 1.2;
}

.dashboard-card-mobile .text-2xl,
.dashboard-card-desktop .text-2xl {
  font-size: clamp(1.65rem, 4.5vw, 2.25rem);
  line-height: 1.2;
}

.dashboard-card-mobile .text-3xl,
.dashboard-card-desktop .text-3xl {
  font-size: clamp(1.9rem, 5.5vw, 2.75rem);
  line-height: 1.15;
}

.dashboard-card-mobile .text-4xl,
.dashboard-card-desktop .text-4xl {
  font-size: clamp(2.15rem, 6.5vw, 3.25rem);
  line-height: 1.1;
}

.dashboard-card-mobile .font-semibold,
.dashboard-card-desktop .font-semibold {
  color: var(--text-primary);
}

.dark .dashboard-card-mobile .font-semibold,
.dark .dashboard-card-desktop .font-semibold {
  color: var(--foreground);
}

.dashboard-card-mobile .text-gray-600,
.dashboard-card-mobile .text-gray-500,
.dashboard-card-desktop .text-gray-600,
.dashboard-card-desktop .text-gray-500 {
  color: var(--text-secondary);
}

.dashboard-card-mobile .text-blue-600,
.dashboard-card-mobile .text-blue-400,
.dashboard-card-mobile .text-red-600,
.dashboard-card-mobile .text-red-400,
.dashboard-card-desktop .text-blue-600,
.dashboard-card-desktop .text-blue-400,
.dashboard-card-desktop .text-red-600,
.dashboard-card-desktop .text-red-400 {
  font-size: clamp(1rem, 3vw, 1.15rem);
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.dashboard-card-hover {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card-hover:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-card-hover);
}

.dashboard-cards-grid .enhanced-calendar-widget,
.dashboard-cards-grid .financial-calculator {
  max-height: 100%;
  overflow: hidden;
}

.square-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: var(--space-sm);
}

.square-card {
  aspect-ratio: 1 / 1;
}

.square-card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.square-card-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-xs);
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: 3;
  -webkit-line-clamp: 3;
}

.text-fit-content {
  font-size: clamp(0.75rem, 2vw, 0.875rem);
  line-height: 1.2;
  word-break: break-word;
  overflow-wrap: anywhere;
}

.text-fit-title {
  font-size: clamp(0.875rem, 2.5vw, 1rem);
  line-height: 1.2;
  word-break: break-word;
  overflow-wrap: anywhere;
}

.text-fit-small {
  font-size: clamp(0.625rem, 1.5vw, 0.75rem);
  line-height: 1.2;
  word-break: break-word;
  overflow-wrap: anywhere;
}

.calendar-day-cell {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.calendar-day-cell:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 14px rgba(59, 130, 246, 0.25);
}

.calendar-day-today {
  animation: pulse-today 2s infinite;
}

@keyframes pulse-today {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }

  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
}

.bill-indicator {
  transition: transform 0.2s ease;
}

.bill-indicator:hover {
  transform: scale(1.15);
}

.calendar-gradient-bg {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
}

.dark .calendar-gradient-bg {
  background: linear-gradient(135deg, rgba(17, 24, 39, 0.95) 0%, rgba(31, 41, 55, 0.95) 100%);
}

.dashboard-card-mobile .enhanced-calendar-container {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  overflow: hidden;
  position: relative;
}

.enhanced-calendar-widget {
  width: 100%;
  height: 100%;
  position: relative;
  contain: layout style;
}

.enhanced-calendar-widget * {
  max-width: 100%;
}

.enhanced-calendar-widget .absolute {
  position: absolute !important;
}

.financial-calculator .btn {
  min-height: 44px;
  min-width: 44px;
  user-select: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.dashboard-modal-ai-assistant,
.dashboard-modal-calculator {
  height: 600px;
  overflow-y: auto;
}

.dashboard-modal-ai-assistant .max-w-4xl {
  max-width: none;
  padding: 0;
}

.dashboard-modal-calculator .financial-calculator {
  max-width: none;
  padding: var(--space-sm);
}

.financial-calculator .text-truncate,
.financial-calculator p,
.financial-calculator h3 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.card-truncate-text {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 100%;
}

.card-truncate-2 {
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.card-truncate-3 {
  display: -webkit-box;
  line-clamp: 3;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.financial-calculator .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.financial-calculator .display-container {
  user-select: none;
  -webkit-user-select: none;
}

.financial-calculator .result {
  word-break: break-all;
  overflow-wrap: break-word;
}

.financial-calculator .history-panel {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.financial-calculator .history-panel::-webkit-scrollbar {
  width: 6px;
}

.financial-calculator .history-panel::-webkit-scrollbar-track {
  background: transparent;
}

.financial-calculator .history-panel::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.financial-calculator .history-panel::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

.bill-form-container {
  @apply bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700;
  backdrop-filter: blur(10px);
}

.bill-type-selector {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.bill-type-selector:hover {
  transform: scale(1.02);
  box-shadow: 0 12px 30px rgba(37, 99, 235, 0.12);
}

.bill-type-selector.selected {
  @apply ring-2 ring-blue-500 ring-offset-2 shadow-lg;
}

.form-field-enhanced {
  position: relative;
}

.form-field-enhanced input,
.form-field-enhanced select,
.form-field-enhanced textarea {
  @apply w-full px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.form-field-enhanced input:focus,
.form-field-enhanced select:focus,
.form-field-enhanced textarea:focus {
  @apply focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  transform: scale(1.01);
  box-shadow: 0 12px 30px rgba(37, 99, 235, 0.15);
}

.btn-modern {
  @apply px-6 py-3 rounded-xl font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btn-modern:hover {
  transform: scale(1.02);
  box-shadow: 0 12px 24px rgba(30, 64, 175, 0.2);
}

.btn-modern:active {
  transform: scale(0.98);
}

.btn-primary {
  @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 focus:ring-gray-500;
}

.payment-summary {
  @apply bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-700 rounded-xl p-4;
  backdrop-filter: blur(6px);
}

.loan-details-section {
  @apply bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl p-6 shadow-inner;
}

.tap-to-open {
  font-size: 0.85rem;
  padding: 0.35rem 0.75rem;
  border-radius: 1rem;
  background-color: rgba(56, 128, 255, 0.12);
  color: var(--theme-color);
  display: inline-flex;
  align-items: center;
  gap: 0.35rem;
  margin-top: 0.5rem;
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.tap-to-open:hover {
  background-color: rgba(56, 128, 255, 0.2);
  transform: translateY(-1px);
}

.nav-card {
  position: relative;
  overflow: hidden;
}

.nav-card::after {
  content: '';
  position: absolute;
  inset: auto 0 0 auto;
  width: 12px;
  height: 12px;
  border-bottom: 2px solid var(--theme-color);
  border-right: 2px solid var(--theme-color);
  opacity: 0.6;
  border-bottom-right-radius: 4px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
  padding: 0.75rem;
}

@media (min-width: 640px) {
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    padding: 1rem;
  }
}

@media (min-width: 768px) {
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.25rem;
    padding: 1.25rem;
  }
}

@media (min-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
  }
}

@media (max-width: 767px) {
  :root {
    --tile-min-width: clamp(150px, 60vw, 220px);
    --tile-min-height: clamp(180px, 60vw, 240px);
  }

  .mobile-footer {
    margin-bottom: clamp(6rem, 18vw, 7.5rem);
    padding-bottom: var(--space-md);
    position: relative;
    z-index: 1;
  }

  body {
    overflow-x: hidden;
    padding-bottom: 0 !important;
  }

  .mobile-footer .py-4 {
    padding-block: var(--space-sm);
  }

  .mobile-footer .mt-4 {
    margin-top: var(--space-xs) !important;
  }

  .mobile-footer .pt-4 {
    padding-top: var(--space-xs) !important;
  }

  .dashboard-cards-grid {
    padding: var(--space-sm);
    gap: var(--space-sm);
  }

  .dashboard-card-mobile,
  .dashboard-card-desktop {
    min-height: clamp(200px, 62vw, 260px);
  }

  .dashboard-card-full-width {
    grid-column: 1 / -1;
  }

  .dashboard-card-mobile button,
  .dashboard-card-mobile [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  .dashboard-card-mobile h1,
  .dashboard-card-mobile h2,
  .dashboard-card-mobile h3,
  .dashboard-card-mobile h4,
  .dashboard-card-mobile h5,
  .dashboard-card-mobile h6 {
    font-size: var(--text-lg);
    line-height: 1.3;
    font-weight: 600;
    margin: var(--space-xs) 0;
  }

  .dashboard-card-mobile p {
    font-size: var(--text-md);
    line-height: 1.45;
    margin: var(--space-xs) 0;
    color: var(--text-secondary);
  }

  .dashboard-card-mobile .body-sm,
  .dashboard-card-mobile .preview-text,
  .dashboard-card-mobile .action-text {
    font-size: var(--text-sm);
    line-height: 1.4;
  }

  .dashboard-cards-container {
    min-height: auto;
    height: auto;
    max-height: none;
  }

  .min-h-screen {
    min-height: auto !important;
  }

  .dashboard-page-mobile {
    min-height: 100dvh;
    overflow-x: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }

  .grid.grid-cols-2 {
    flex: 1 0 auto;
    overflow: visible;
  }

  .tile-title {
    font-size: var(--text-md);
  }

  .tile-subtitle {
    font-size: var(--text-sm);
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  :root {
    --tile-min-width: clamp(200px, 34vw, 260px);
    --tile-min-height: clamp(220px, 36vw, 300px);
  }

  .dashboard-card-full-width {
    grid-column: 1 / span 2;
  }
}

@media (min-width: 1024px) {
  :root {
    --tile-min-width: clamp(220px, 24vw, 300px);
    --tile-min-height: clamp(240px, 28vw, 320px);
  }

  .dashboard-card-full-width {
    grid-column: 2 / span 2;
  }
}

@media (min-width: 1440px) {
  :root {
    --tile-min-width: clamp(240px, 20vw, 340px);
    --tile-min-height: clamp(260px, 24vw, 360px);
  }
}

@media (max-width: 640px) {
  .dashboard-card-hover:hover {
    transform: none;
    box-shadow: var(--shadow-card);
  }

  .dashboard-card-hover:active {
    transform: scale(0.97);
  }

  .mobile-card-spacing > * + * {
    margin-top: var(--space-sm) !important;
  }

  .touch-target,
  .clickable,
  button,
  .btn,
  [role="button"],
  input[type="button"],
  input[type="submit"] {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .tile-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--space-xs);
    width: 100%;
    height: 100%;
  }

  textarea {
    min-height: 44px;
  }
}

.dashboard-expanded-content {
  max-height: calc(100vh - 8rem);
  overflow-y: auto;
  padding: var(--space-md);
}

@media (max-width: 768px) {
  .dashboard-expanded-content {
    max-height: calc(100vh - 6rem);
    padding: var(--space-sm);
    font-size: var(--text-sm);
  }

  .dashboard-expanded-content .calendar-compact .rbc-date-cell {
    padding: 0.125rem !important;
    font-size: 0.625rem !important;
  }

  .dashboard-expanded-content .calendar-compact .rbc-header {
    padding: 0.25rem 0.125rem !important;
    font-size: 0.625rem !important;
  }
}

.dashboard-expanded-content .calendar-compact {
  height: 100%;
  overflow: hidden;
}

.dashboard-expanded-content .calendar-compact .rbc-month-row {
  flex: 1 !important;
  min-height: 0 !important;
}

.dashboard-expanded-content .calendar-compact .rbc-date-cell {
  padding: 0.25rem !important;
  font-size: 0.75rem !important;
}

.dashboard-expanded-content .calendar-compact .rbc-header {
  padding: 0.5rem 0.25rem !important;
  font-size: 0.75rem !important;
}

.dashboard-cards-grid > *:last-child:nth-child(odd) {
  grid-column: auto;
}

@media (max-width: 767px) {
  .dashboard-cards-grid > *:last-child:nth-child(odd) {
    grid-column: 1 / -1;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .dashboard-cards-grid > *:last-child:nth-child(3n + 1) {
    grid-column: 2 / span 2;
  }
}

@media (min-width: 1024px) {
  .dashboard-cards-grid > *:last-child:nth-child(4n + 1) {
    grid-column: 2 / span 2;
  }
}

.tile-span-2 {
  grid-column: span 2;
}

.tile-start-2-span-2 {
  grid-column: 2 / span 2;
}

.tile-scroll {
  overflow: auto;
}
/* Chrome Extension Interference Fixes */
/* Hide chrome extension elements that can interfere with PWA */
div[id*="chrome-extension"],
iframe[src*="chrome-extension"],
script[src*="chrome-extension"],
link[href*="chrome-extension"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
}

/* Prevent chrome extension console errors from affecting layout */
body {
  position: relative !important;
  z-index: 1 !important;
}

/* Override any extension-inserted styles that might break scrolling */
html {
  overflow-x: hidden !important;
  overflow-y: auto !important;
  position: relative !important;
}

body {
  overflow-x: hidden !important;
  overflow-y: auto !important;
  position: relative !important;
  height: 100% !important;
}

/* Mobile UI/UX Cleanup - Specific Fixes for PWA Layout */
@media (max-width: 640px) {

  /* Prevent horizontal scroll and ensure proper viewport */
  html,
  body {
    max-width: 100vw;
    overflow-x: hidden;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  /* Mobile Dashboard Specific Fixes */
  .mobile-dashboard-container {
    padding-bottom: 100px !important;
    /* Space for bottom navigation */
    min-height: 100vh;
  }

  /* Fix overlapping wallet section */
  .mobile-wallet-section {
    position: relative !important;
    margin-top: 2rem !important;
    padding-top: 2rem !important;
  }

  /* Ensure proper spacing between sections */
  .mobile-dashboard-section {
    margin-bottom: 2rem !important;
  }

  /* Bottom navigation improvements - Fixed at bottom of screen */
  .mobile-bottom-nav {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 9999 !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
    padding: 0.75rem 1rem !important;
    box-shadow: 0 -10px 25px -5px rgba(0, 0, 0, 0.1), 0 -4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    pointer-events: auto !important;
    transform: translateZ(0) !important;
  }

  .dark .mobile-bottom-nav {
    background: rgba(17, 24, 39, 0.95) !important;
    border-top-color: rgba(255, 255, 255, 0.1) !important;
  }

  /* Floating action button placement */
  .mobile-fab {
    position: fixed !important;
    right: 1.25rem !important;
    bottom: 6.5rem !important;
    transform: translateZ(0) !important;
    z-index: 10040 !important;
    pointer-events: auto !important;
  }

  /* Header section fixes */
  .mobile-header-gradient {
    padding-bottom: 6rem !important;
    /* Increase bottom padding */
  }

  /* Card spacing improvements */
  .mobile-card-grid {
    gap: 1rem !important;
  }

  /* Text readability improvements */
  .mobile-text-contrast {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* Prevent layout shift */
  .mobile-section-spacing>*+* {
    margin-top: 1.5rem !important;
  }

  /* Safe area padding for devices with notches */
  @supports (padding: max(0px)) {
    .mobile-bottom-nav {
      padding-bottom: max(1rem, env(safe-area-inset-bottom)) !important;
    }

    .mobile-dashboard-container {
      padding-bottom: max(140px, calc(140px + env(safe-area-inset-bottom))) !important;
    }

    .mobile-fab {
      bottom: calc(6.5rem + env(safe-area-inset-bottom)) !important;
    }
  }

  /* Improve touch targets */
  button,
  [role="button"],
  .btn {
    min-height: 44px !important;
    min-width: 44px !important;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
  }

  /* Improve form inputs */
  input,
  select,
  textarea {
    font-size: 16px !important;
    /* Prevents zoom on iOS */
    border-radius: 0.5rem !important;
    padding: 0.75rem !important;
  }

  /* Card improvements */
  .card,
  .bg-white {
    border-radius: 1.5rem !important;
    padding: 1rem !important;
    margin-bottom: 1rem !important;
  }

  /* Navigation improvements */
  nav a,
  .nav-link {
    padding: 0.75rem 1rem !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Modal improvements */
  .modal,
  .dialog {
    max-height: 90vh !important;
    max-width: 95vw !important;
    margin: 2rem auto !important;
  }

  /* Typography improvements */
  h1,
  h2,
  h3 {
    line-height: 1.2 !important;
    word-break: break-word;
  }

  /* Animation performance */
  .mobile-dashboard-container,
  .mobile-dashboard-container * {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  .mobile-bottom-nav,
  .mobile-bottom-nav * {
    -webkit-transform: none !important;
    transform: none !important;
  }

  /* Optimize animations for mobile */
  .transition-transform,
  .transition-all,
  .transition-colors,
  .transition-shadow {
    will-change: transform, opacity, box-shadow !important;
  }

  /* Remove animation on reduced motion preference */
  @media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Fix specific overlapping issues from the mobile screenshot */
  .space-y-6>*+* {
    margin-top: 2rem !important;
    /* Increase spacing between sections */
  }

  /* Ensure gradient header has proper bottom margin */
  .mobile-header-gradient {
    margin-bottom: 2rem !important;
  }

  /* Fix wallet cards spacing and prevent overflow */
  .space-y-3>*+* {
    margin-top: 1rem !important;
  }

  /* Ensure proper z-index stacking */
  .mobile-bottom-nav {
    z-index: 9999 !important;
  }

  /* Prevent content from going behind nav */
  body {
    padding-bottom: 0 !important;
    /* Let mobile container handle this */
  }

  /* Fix for cards that might be too close to edges */
  .rounded-3xl {
    margin-left: 0.25rem !important;
    margin-right: 0.25rem !important;
  }

  /* Ensure proper scrolling behavior on mobile */
  .mobile-dashboard-container {
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    height: auto !important;
    min-height: 100vh !important;
    overscroll-behavior: contain !important;
  }

  /* Fix potential layout shifts */
  .mobile-section-spacing {
    will-change: transform !important;
    transform: translateZ(0) !important;
  }

  /* Ensure all interactive elements work */
  button,
  [role="button"],
  .btn {
    pointer-events: auto !important;
    cursor: pointer !important;
  }

  /* Fix z-index stacking context issues */
  .mobile-dashboard-container * {
    position: relative !important;
    z-index: auto !important;
  }

  /* Prevent content from being hidden behind overlays */
  .space-y-6 {
    z-index: 1 !important;
    position: relative !important;
  }

  /* Ensure no container prevents fixed navigation */
  .sm\\:hidden {
    position: relative !important;
    z-index: 1 !important;
  }

  /* Force mobile bottom nav to be truly fixed */
  .mobile-bottom-nav {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 99999 !important;
    transform: none !important;
    will-change: auto !important;
  }

  /* Safe area padding for devices with notches */
  @supports (padding: max(0px)) {
    .mobile-bottom-nav {
      padding-bottom: max(0.75rem, env(safe-area-inset-bottom)) !important;
    }

    .mobile-dashboard-container {
      padding-bottom: max(100px, calc(100px + env(safe-area-inset-bottom))) !important;
    }

    .mobile-footer {
      margin-bottom: max(100px, calc(100px + env(safe-area-inset-bottom))) !important;
    }

    .mobile-fab {
      bottom: calc(6.5rem + env(safe-area-inset-bottom)) !important;
    }
  }
}

