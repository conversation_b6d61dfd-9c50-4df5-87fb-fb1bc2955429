{"name": "payday-pilot-next", "version": "0.6.5", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS=\"--max-old-space-size=6144 --no-deprecation\" next dev -p 3000 -H 0.0.0.0 --turbo", "dev:optimized": "cross-env NODE_OPTIONS=\"--max-old-space-size=6144 --no-warnings --no-deprecation --max-http-header-size=16384\" next dev -p 3000 -H 0.0.0.0 --turbo", "dev:fast": "node scripts/dev-fast.js", "dev:lightning": "node scripts/dev-lightning.js", "dev:profile": "cross-env NODE_OPTIONS=\"--inspect --cpu-prof --heapsnapshot-signal=SIGUSR2\" next dev", "dev:clean": "rimraf .next build-out .turbo node_modules/.cache && npm run dev:lightning", "dev:ultrafast": "cross-env NEXT_SKIP_TSC=1 NODE_ENV=development NEXT_TELEMETRY_DISABLED=1 NEXT_MINIMAL_TRANSPILATION=1 MOCK_FIREBASE=true NODE_OPTIONS=\"--max-old-space-size=8192 --no-warnings\" next dev --turbo", "dev:turbo": "cross-env NEXT_SKIP_TSC=1 NEXT_TELEMETRY_DISABLED=1 NEXT_MINIMAL_TRANSPILATION=1 MOCK_FIREBASE=true NEXT_DISABLE_ESLINT=1 NODE_OPTIONS=\"--max-old-space-size=10240 --no-warnings --no-deprecation\" next dev --turbo -p 3000", "fix:build": "node scripts/fix-build.js", "build": "cross-env NODE_OPTIONS=\"--max-old-space-size=6144\" next build", "build:analyze": "cross-env ANALYZE=true NODE_OPTIONS=\"--max-old-space-size=6144\" next build", "start": "cross-env NODE_OPTIONS=\"--max-old-space-size=2048\" next start", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "deploy": "netlify deploy --prod", "bump:patch": "npm version patch --no-git-tag-version && node scripts/bump-version.js", "bump:minor": "npm version minor --no-git-tag-version && node scripts/bump-version.js", "bump:major": "npm version major --no-git-tag-version && node scripts/bump-version.js", "version:bump": "node scripts/bump-version.js", "release": "standard-version", "profile:analyze": "0x --visualize-cpu-profile .next/cpu-profile.cpuprofile", "perf:test": "node scripts/performance-monitor.js test", "perf:dev": "node scripts/performance-monitor.js dev", "perf:build": "node scripts/performance-monitor.js build", "cache:optimize": "node scripts/optimize-cache.js", "dev:superfast": "npm run cache:optimize && npm run dev:lightning"}, "dependencies": {"@headlessui/react": "^2.2.1", "@heroicons/react": "^2.2.0", "browserslist": "^4.26.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.24", "date-fns": "^4.1.0", "expo-server-sdk": "^3.15.0", "firebase": "^11.6.1", "firebase-admin": "^13.3.0", "firebase-functions": "^6.3.2", "framer-motion": "^12.9.1", "googleapis": "^148.0.0", "marked": "^15.0.11", "next": "^14.2.31", "next-pwa": "^5.6.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.18.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-onesignal": "^3.2.2", "recharts": "^2.15.3", "sharp": "^0.34.1", "styled-components": "^6.1.17", "swr": "^2.3.3", "tailwind-merge": "^3.2.0", "tar-fs": "^3.1.0", "uuid": "^9.0.1", "web-push": "^3.6.7", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^2.1.4", "@netlify/functions": "^3.1.2", "@netlify/plugin-nextjs": "5.10.6", "@next/bundle-analyzer": "^15.3.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-big-calendar": "^1.16.1", "@types/react-dom": "^18.2.18", "@types/uuid": "^10.0.0", "@types/web-push": "^3.6.4", "autoprefixer": "^10.4.21", "critters": "^0.0.23", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "netlify-cli": "^23.1.3", "postcss": "^8.4.32", "postcss-nesting": "^13.0.1", "rimraf": "^6.0.1", "standard-version": "^9.5.0", "stylelint-config-standard": "^38.0.0", "tailwindcss": "^3.4.0", "typescript": "^5.3.3"}}